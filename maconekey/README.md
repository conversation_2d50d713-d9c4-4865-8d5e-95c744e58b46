# Mac 开发环境一键安装脚本

一个简单的 macOS 开发环境自动配置脚本。

## 功能

- 🍺 自动安装 Homebrew（使用国内源）
- 📦 安装 Node.js 22
- ⚡ 配置 npm 和 pnpm（使用淘宝源）
- 🗄️ 下载并安装最新版 PocketBase

## 使用方法

```bash
chmod +x install_mac.sh
./install_mac.sh
```

## 安装内容

- **Homebrew**: macOS 包管理器
- **Node.js 22**: JavaScript 运行环境
- **pnpm**: 快速的包管理器
- **PocketBase**: 轻量级后端服务
- **jq**: JSON 处理工具

## 注意事项

- 脚本会自动检测已安装的软件，避免重复安装
- 使用国内镜像源，提高下载速度
- PocketBase 安装到 `~/Documents/Code/pocketbase/` 目录

## 系统要求

- macOS 系统
- 网络连接

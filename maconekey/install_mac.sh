#!/usr/bin/env bash

set -euo pipefail

info() { echo "[INFO] $*"; }
warn() { echo "[WARN] $*" >&2; }
error() { echo "[ERROR] $*" >&2; }

# Ensure we're running with zsh for the Homebrew CN installer if needed

ensure_brew() {
  if command -v brew >/dev/null 2>&1; then
    info "Homebrew 已安装: $(brew --version | head -n1)"
  else
    warn "未检测到 Homebrew，开始安装 (使用国内脚本)..."
    /bin/zsh -c "$(curl -fsSL https://gitee.com/cunkai/HomebrewCN/raw/master/Homebrew.sh)"
  fi

  # 初始化 brew 到当前 shell 环境
  if [ -x "/opt/homebrew/bin/brew" ]; then
    eval "$(/opt/homebrew/bin/brew shellenv)"
  elif [ -x "/usr/local/bin/brew" ]; then
    eval "$(/usr/local/bin/brew shellenv)"
  fi

  if ! command -v brew >/dev/null 2>&1; then
    error "Homebrew 安装或初始化失败，请手动检查后重试。"
    exit 1
  fi
}

install_node22() {
  info "安装 Node.js 22 (通过 Homebrew)"
  if brew list --versions node@22 >/dev/null 2>&1; then
    info "node@22 已安装"
  else
    brew install node@22
  fi
  brew link --overwrite --force node@22 || true

  if ! command -v node >/dev/null 2>&1; then
    error "Node.js 未能正确链接到 PATH。请检查 brew 配置。"
    exit 1
  fi
  info "Node 版本: $(node -v)"
}

configure_npm_and_pnpm() {
  info "配置 npm 使用淘宝源 (npmmirror)"
  npm config set registry https://registry.npmmirror.com/

  info "通过 npm 全局安装 pnpm"
  if command -v pnpm >/dev/null 2>&1; then
    info "pnpm 已安装: $(pnpm -v)"
  else
    npm i -g pnpm
  fi

  info "配置 pnpm 使用淘宝源 (npmmirror)"
  pnpm config set registry https://registry.npmmirror.com/
}

ensure_jq() {
  if ! command -v jq >/dev/null 2>&1; then
    info "安装 jq (用于更好的 JSON 解析)"
    brew install jq || warn "jq 安装失败，将使用备用 JSON 解析方法"
  fi
}

setup_pocketbase() {
  local base_dir
  base_dir="${HOME}/Documents/Code"
  local target_dir
  target_dir="${base_dir}/pocketbase"
  mkdir -p "${target_dir}"
  info "PocketBase 目录: ${target_dir}"

  # 获取最新版本号
  info "正在获取 PocketBase 最新版本号..."
  local latest_version
  # 使用更健壮的方式获取最新版本，支持多种 JSON 解析方法
  if command -v jq >/dev/null 2>&1; then
    # 如果有 jq，使用 jq 解析 JSON
    latest_version=$(curl -s https://api.github.com/repos/pocketbase/pocketbase/releases/latest | jq -r '.tag_name' 2>/dev/null || echo "")
  else
    # 使用改进的正则表达式，更精确地匹配版本号
    latest_version=$(curl -s https://api.github.com/repos/pocketbase/pocketbase/releases/latest | grep -o '"tag_name"[[:space:]]*:[[:space:]]*"v[0-9]\+\.[0-9]\+\.[0-9]\+"' | cut -d '"' -f 4 2>/dev/null || echo "")
  fi

  # 如果获取失败，使用更新的备用版本
  if [ -z "${latest_version}" ] || [ "${latest_version}" = "null" ]; then
    warn "无法获取最新版本，使用备用版本"
    latest_version="v0.23.0"  # 更新备用版本
  fi

  info "目标版本: ${latest_version}"

  # 移除版本号前缀 'v'
  local version_number
  version_number=${latest_version#v}

  # 根据系统架构确定下载文件名
  local arch
  arch="$(uname -m)"
  local zip_name
  if [ "${arch}" = "arm64" ]; then
    zip_name="pocketbase_${version_number}_darwin_arm64.zip"
  else
    zip_name="pocketbase_${version_number}_darwin_amd64.zip"
  fi

  # 检查是否已安装相同版本
  local pocketbase_path="${target_dir}/pocketbase"
  if [ -f "${pocketbase_path}" ]; then
    local current_version
    current_version=$("${pocketbase_path}" --version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' || echo "unknown")
    if [ "${current_version}" = "${latest_version}" ]; then
      info "PocketBase ${latest_version} 已是最新版本，跳过下载"
      return 0
    else
      info "当前版本: ${current_version}，需要更新到: ${latest_version}"
    fi
  fi

  # 构建完整的下载URL
  local url
  url="https://github.com/pocketbase/pocketbase/releases/download/${latest_version}/${zip_name}"
  local zip_path
  zip_path="${target_dir}/${zip_name}"

  info "下载 PocketBase: ${url}"
  if ! curl -fL --retry 3 --connect-timeout 30 -o "${zip_path}" "${url}"; then
    error "下载失败，请检查网络连接或稍后重试"
    exit 1
  fi

  info "解压 PocketBase"
  if ! unzip -o "${zip_path}" -d "${target_dir}" >/dev/null; then
    error "解压失败，下载的文件可能损坏"
    rm -f "${zip_path}"
    exit 1
  fi

  rm -f "${zip_path}"
  chmod +x "${target_dir}/pocketbase" || true

  # 验证安装
  if [ -f "${pocketbase_path}" ] && "${pocketbase_path}" --version >/dev/null 2>&1; then
    local installed_version
    installed_version=$("${pocketbase_path}" --version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' || echo "unknown")
    info "PocketBase 安装成功: ${installed_version} (${target_dir}/pocketbase)"
  else
    error "PocketBase 安装验证失败"
    exit 1
  fi
}

main() {
  info "开始配置 mac 开发环境"
  ensure_brew
  ensure_jq
  install_node22
  configure_npm_and_pnpm
  setup_pocketbase
  info "全部完成 ✅"
}

main "$@"


